<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Liste des Conférences</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
      }

      .container {
        max-width: 1000px;
        margin: 0 auto;
        background-color: white;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
        position: relative;
      }

      .user-info {
        position: absolute;
        top: 0;
        right: 0;
        font-size: 14px;
      }

      .user-info a {
        color: #007bff;
        text-decoration: none;
        margin-left: 10px;
      }

      .user-info a:hover {
        text-decoration: underline;
      }

      h1 {
        color: #333;
        margin-bottom: 10px;
      }

      .header p {
        color: #666;
        margin-bottom: 20px;
      }

      .btn-create {
        background-color: #28a745;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 3px;
        font-weight: bold;
      }

      .btn-create:hover {
        background-color: #218838;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }

      th,
      td {
        padding: 10px;
        text-align: left;
        border: 1px solid #ddd;
      }

      th {
        background-color: #f8f9fa;
        font-weight: bold;
      }

      tr:nth-child(even) {
        background-color: #f9f9f9;
      }

      tr:hover {
        background-color: #e9ecef;
      }

      a {
        color: #007bff;
        text-decoration: none;
        padding: 5px 10px;
        border-radius: 3px;
        margin: 2px;
        display: inline-block;
      }

      a:hover {
        text-decoration: underline;
      }

      .btn-detail {
        background-color: #17a2b8;
        color: white;
      }

      .btn-detail:hover {
        background-color: #138496;
        text-decoration: none;
      }

      .btn-update {
        background-color: #ffc107;
        color: #212529;
      }

      .btn-update:hover {
        background-color: #e0a800;
        text-decoration: none;
      }

      .btn-delete {
        background-color: #dc3545;
        color: white;
      }

      .btn-delete:hover {
        background-color: #c82333;
        text-decoration: none;
      }

      .price {
        font-weight: bold;
        color: #28a745;
      }

      .empty-message {
        text-align: center;
        padding: 20px;
        color: #666;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="user-info">
          {% if user.is_authenticated %}
          <span>Bonjour {{ user.first_name }} {{ user.last_name }}</span>
          <a href="{% url 'logout' %}">Déconnexion</a>
          {% else %}
          <a href="{% url 'login' %}">Connexion</a>
          <a href="{% url 'register' %}">Inscription</a>
          {% endif %}
        </div>
        <h1>Gestion des Conférences</h1>
        <p>Tableau de bord administrateur</p>
        <a
          href="{% url 'conference_create' %}"
          class="btn-create">
          Créer une Nouvelle Conférence
        </a>
      </div>

      <table>
        <thead>
          <tr>
            <th>Titre</th>
            <th>Catégorie</th>
            <th>Date début</th>
            <th>Date fin</th>
            <th>Prix</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for conference in conferences %}
          <tr>
            <td>{{ conference.title }}</td>
            <td>{{ conference.category.title }}</td>
            <td>{{ conference.start_date }}</td>
            <td>{{ conference.end_date }}</td>
            <td class="price">{{ conference.price }} €</td>
            <td>
              <a
                href="{% url 'conference_detail' conference.id %}"
                class="btn-detail"
                >Détail</a
              >
              <a
                href="{% url 'conference_update' conference.id %}"
                class="btn-update"
                >Modifier</a
              >
              <a
                href="{% url 'conference_delete' conference.id %}"
                class="btn-delete"
                >Supprimer</a
              >
            </td>
          </tr>
          {% empty %}
          <tr>
            <td
              colspan="6"
              class="empty-message">
              Aucune conférence disponible.
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </body>
</html>
