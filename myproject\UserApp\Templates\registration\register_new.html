<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Inscription - Gestion des Conférences</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
      }

      .register-container {
        max-width: 500px;
        width: 100%;
        background: white;
        padding: 30px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      .register-header {
        text-align: center;
        margin-bottom: 30px;
      }

      .register-header h1 {
        color: #333;
        margin-bottom: 10px;
      }

      .register-header p {
        color: #666;
      }

      form p {
        margin-bottom: 15px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }

      input,
      select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        font-size: 14px;
      }

      input:focus,
      select:focus {
        border-color: #007bff;
        outline: none;
      }

      button[type="submit"] {
        width: 100%;
        padding: 10px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 3px;
        font-size: 16px;
        cursor: pointer;
        margin-top: 10px;
      }

      button[type="submit"]:hover {
        background-color: #0056b3;
      }

      .errorlist {
        color: red;
        font-size: 12px;
        margin-top: 5px;
        list-style: none;
        padding: 0;
      }

      .messages {
        margin-bottom: 20px;
      }

      .alert {
        padding: 10px;
        border-radius: 3px;
        margin-bottom: 10px;
      }

      .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .alert-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .auth-links {
        text-align: center;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
      }

      .auth-links a {
        color: #007bff;
        text-decoration: none;
      }

      .auth-links a:hover {
        text-decoration: underline;
      }
    </style>
  </head>
  <body>
    <div class="register-container">
      <div class="register-header">
        <h1>Inscription</h1>
        <p>Créez votre compte pour accéder aux conférences</p>
      </div>

      {% if messages %}
      <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
      </div>
      {% endif %}

      <form method="post">
        {% csrf_token %} {{ form.as_p }}
        <button type="submit">S'inscrire</button>
      </form>

      <div class="auth-links">
        <p>Déjà un compte ? <a href="{% url 'login' %}">Se connecter</a></p>
      </div>
    </div>
  </body>
</html>
