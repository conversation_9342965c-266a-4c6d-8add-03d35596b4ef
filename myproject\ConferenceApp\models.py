from django.db import models
from django.utils import timezone
from UserApp.models import Participant
from django.core.exceptions import ValidationError
from django.core.validators import MinLengthValidator
import re

def validate_letters(value):
    """Valide qu'un champ contient seulement des lettres et des espaces"""
    if not re.match(r'^[a-zA-Z ]+$', value):
        raise ValidationError('This field should only contain letters and spaces.')

def validate_pdf_extension(value):
    """Valide que le fichier a une extension PDF"""
    if not value.name.lower().endswith('.pdf'):
        raise ValidationError('Only PDF files are allowed.')

def validate_start_date(value):
    if value < timezone.now():
        raise ValidationError('Start date cannot be in the past.')

class Category(models.Model):
    title = models.CharField(max_length=200, validators=[validate_letters])

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Categories"

    def __str__(self):
        return self.title

class Conference(models.Model):
    title = models.CharField(
        max_length=250,
        validators=[
            validate_letters,
            MinLengthValidator(5, message='Title must be at least 5 characters long.')
        ]
    )
    description = models.TextField(blank=True)
    start_date = models.DateTimeField(default=timezone.now, validators=[validate_start_date])
    end_date = models.DateTimeField(default=timezone.now)
    location = models.CharField(max_length=250)
    price = models.FloatField()
    capacity = models.IntegerField()
    program = models.FileField(upload_to='files/', validators=[validate_pdf_extension], blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='conferences')
    reservations = models.ManyToManyField(Participant, through='Reservation', related_name='reservations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Reservation(models.Model):
    confirmed = models.BooleanField(default=False)
    reservation_date = models.DateTimeField(default=timezone.now)
    conference = models.ForeignKey(Conference, on_delete=models.CASCADE)
    participant = models.ForeignKey(Participant, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('conference', 'participant')

    def __str__(self):
        return f"{self.participant} - {self.conference} ({'Confirmed' if self.confirmed else 'Pending'})"

