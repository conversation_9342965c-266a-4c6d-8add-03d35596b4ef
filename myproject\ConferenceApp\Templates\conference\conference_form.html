<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Formulaire Conférence</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
      }

      .container {
        max-width: 600px;
        margin: 0 auto;
        background-color: white;
        padding: 30px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
      }

      form p {
        margin-bottom: 15px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }

      input,
      select,
      textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        font-size: 14px;
      }

      input:focus,
      select:focus,
      textarea:focus {
        border-color: #007bff;
        outline: none;
      }

      textarea {
        resize: vertical;
        min-height: 100px;
      }

      button {
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 3px;
        font-size: 16px;
        cursor: pointer;
        display: block;
        margin: 20px auto 0;
      }

      button:hover {
        background-color: #0056b3;
      }

      .back-link {
        text-align: center;
        margin-top: 20px;
      }

      .back-link a {
        color: #007bff;
        text-decoration: none;
      }

      .back-link a:hover {
        text-decoration: underline;
      }

      .errorlist {
        color: red;
        font-size: 12px;
        margin-top: 5px;
        list-style: none;
        padding: 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Créer une Conférence</h1>
      <form method="post">
        {% csrf_token %} {{ form.as_p }}
        <button type="submit">Créer</button>
      </form>
      <div class="back-link">
        <a href="{% url 'conference_class_list' %}">Retour à la liste</a>
      </div>
    </div>
  </body>
</html>
