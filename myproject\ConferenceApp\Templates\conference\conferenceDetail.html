<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>{{ conference.title }} - Détails</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: white;
        padding: 30px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
      }

      .header h1 {
        color: #333;
        margin-bottom: 10px;
      }

      .conference-title {
        color: #007bff;
        margin: 0 0 15px 0;
        font-size: 2em;
      }

      .conference-category {
        background-color: #007bff;
        color: white;
        padding: 5px 15px;
        border-radius: 15px;
        display: inline-block;
        font-size: 0.9em;
        font-weight: bold;
      }

      .conference-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin: 20px 0;
      }

      .info-item {
        background: #f8f9fa;
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 5px;
      }

      .info-label {
        font-weight: bold;
        color: #555;
        margin-bottom: 5px;
      }

      .info-value {
        color: #333;
      }

      .conference-description {
        background: #f8f9fa;
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 5px;
        margin: 20px 0;
      }

      .btn {
        display: inline-block;
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 3px;
        margin: 5px;
      }

      .btn:hover {
        background-color: #0056b3;
        text-decoration: none;
      }

      .btn-secondary {
        background-color: #6c757d;
      }

      .btn-secondary:hover {
        background-color: #545b62;
      }

      .btn-success {
        background-color: #28a745;
      }

      .btn-success:hover {
        background-color: #1e7e34;
      }

      .btn-warning {
        background-color: #ffc107;
        color: #212529;
      }

      .btn-warning:hover {
        background-color: #e0a800;
      }

      .actions-section {
        text-align: center;
        margin-top: 30px;
      }

      @media (max-width: 768px) {
        .conference-info {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Détails de la Conférence</h1>
      </div>

      <div class="conference-header">
        <h1 class="conference-title">{{ conference.title }}</h1>
        <span class="conference-category">{{ conference.category.title }}</span>
      </div>

      <div class="conference-info">
        <div class="info-item">
          <div class="info-label">Date de début</div>
          <div class="info-value">
            {{ conference.start_date|date:"d/m/Y à H:i" }}
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">Date de fin</div>
          <div class="info-value">
            {{ conference.end_date|date:"d/m/Y à H:i" }}
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">Lieu</div>
          <div class="info-value">{{ conference.location }}</div>
        </div>

        <div class="info-item">
          <div class="info-label">Prix</div>
          <div class="info-value">{{ conference.price }} €</div>
        </div>

        <div class="info-item">
          <div class="info-label">Capacité</div>
          <div class="info-value">{{ conference.capacity }} participants</div>
        </div>

        <div class="info-item">
          <div class="info-label">Réservations</div>
          <div class="info-value">
            {{ conference.reservation_set.count }} / {{ conference.capacity }}
            {% if conference.reservation_set.count >= conference.capacity %}
            <span style="color: red">(Complet)</span>
            {% endif %}
          </div>
        </div>
      </div>

      {% if conference.description %}
      <div class="conference-description">
        <h3>Description</h3>
        <p>{{ conference.description|linebreaks }}</p>
      </div>
      {% endif %} {% if conference.program %}
      <div style="margin-bottom: 20px">
        <h3>Programme</h3>
        <a
          href="{{ conference.program.url }}"
          class="btn btn-success"
          target="_blank">
          Télécharger le programme (PDF)
        </a>
      </div>
      {% endif %}

      <div class="actions-section">
        <a
          href="{% url 'conference_class_list' %}"
          class="btn btn-secondary">
          Retour à la liste
        </a>

        {% if user.is_staff %}
        <a
          href="/admin/ConferenceApp/conference/{{ conference.id }}/change/"
          class="btn btn-warning">
          Modifier (Admin)
        </a>
        {% endif %}
      </div>
    </div>
  </body>
</html>
